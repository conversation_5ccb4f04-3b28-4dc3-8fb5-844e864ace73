Defaulting to user installation because normal site-packages is not writeable
Collecting litellm
  Downloading litellm-1.73.6-py3-none-any.whl.metadata (39 kB)
Requirement already satisfied: aiohttp>=3.10 in /home/<USER>/.local/lib/python3.13/site-packages (from litellm) (3.12.13)
Requirement already satisfied: click in /home/<USER>/.local/lib/python3.13/site-packages (from litellm) (8.2.1)
Requirement already satisfied: httpx>=0.23.0 in /home/<USER>/.local/lib/python3.13/site-packages (from litellm) (0.28.1)
Requirement already satisfied: importlib-metadata>=6.8.0 in /home/<USER>/.local/lib/python3.13/site-packages (from litellm) (8.7.0)
Requirement already satisfied: jinja2<4.0.0,>=3.1.2 in /home/<USER>/.local/lib/python3.13/site-packages (from litellm) (3.1.6)
Requirement already satisfied: jsonschema<5.0.0,>=4.22.0 in /usr/lib/python3.13/site-packages (from litellm) (4.23.0)
Collecting openai>=1.68.2 (from litellm)
  Downloading openai-1.93.0-py3-none-any.whl.metadata (29 kB)
Requirement already satisfied: pydantic<3.0.0,>=2.0.0 in /home/<USER>/.local/lib/python3.13/site-packages (from litellm) (2.11.7)
Requirement already satisfied: python-dotenv>=0.2.0 in /home/<USER>/.local/lib/python3.13/site-packages (from litellm) (1.1.0)
Requirement already satisfied: tiktoken>=0.7.0 in /home/<USER>/.local/lib/python3.13/site-packages (from litellm) (0.9.0)
Requirement already satisfied: tokenizers in /home/<USER>/.local/lib/python3.13/site-packages (from litellm) (0.21.1)
Requirement already satisfied: MarkupSafe>=2.0 in /usr/lib/python3.13/site-packages (from jinja2<4.0.0,>=3.1.2->litellm) (3.0.2)
Requirement already satisfied: attrs>=22.2.0 in /usr/lib/python3.13/site-packages (from jsonschema<5.0.0,>=4.22.0->litellm) (25.3.0)
Requirement already satisfied: jsonschema-specifications>=2023.03.6 in /usr/lib/python3.13/site-packages (from jsonschema<5.0.0,>=4.22.0->litellm) (2024.10.1)
Requirement already satisfied: referencing>=0.28.4 in /usr/lib/python3.13/site-packages (from jsonschema<5.0.0,>=4.22.0->litellm) (0.35.1)
Requirement already satisfied: rpds-py>=0.7.1 in /usr/lib/python3.13/site-packages (from jsonschema<5.0.0,>=4.22.0->litellm) (0.22.3)
Requirement already satisfied: annotated-types>=0.6.0 in /home/<USER>/.local/lib/python3.13/site-packages (from pydantic<3.0.0,>=2.0.0->litellm) (0.7.0)
Requirement already satisfied: pydantic-core==2.33.2 in /home/<USER>/.local/lib/python3.13/site-packages (from pydantic<3.0.0,>=2.0.0->litellm) (2.33.2)
Requirement already satisfied: typing-extensions>=4.12.2 in /usr/lib/python3.13/site-packages (from pydantic<3.0.0,>=2.0.0->litellm) (4.13.2)
Requirement already satisfied: typing-inspection>=0.4.0 in /home/<USER>/.local/lib/python3.13/site-packages (from pydantic<3.0.0,>=2.0.0->litellm) (0.4.1)
Requirement already satisfied: aiohappyeyeballs>=2.5.0 in /home/<USER>/.local/lib/python3.13/site-packages (from aiohttp>=3.10->litellm) (2.6.1)
Requirement already satisfied: aiosignal>=1.1.2 in /home/<USER>/.local/lib/python3.13/site-packages (from aiohttp>=3.10->litellm) (1.3.2)
Requirement already satisfied: frozenlist>=1.1.1 in /home/<USER>/.local/lib/python3.13/site-packages (from aiohttp>=3.10->litellm) (1.7.0)
Requirement already satisfied: multidict<7.0,>=4.5 in /home/<USER>/.local/lib/python3.13/site-packages (from aiohttp>=3.10->litellm) (6.5.0)
Requirement already satisfied: propcache>=0.2.0 in /home/<USER>/.local/lib/python3.13/site-packages (from aiohttp>=3.10->litellm) (0.3.2)
Requirement already satisfied: yarl<2.0,>=1.17.0 in /home/<USER>/.local/lib/python3.13/site-packages (from aiohttp>=3.10->litellm) (1.20.1)
Requirement already satisfied: idna>=2.0 in /usr/lib/python3.13/site-packages (from yarl<2.0,>=1.17.0->aiohttp>=3.10->litellm) (3.10)
Requirement already satisfied: anyio in /home/<USER>/.local/lib/python3.13/site-packages (from httpx>=0.23.0->litellm) (4.9.0)
Requirement already satisfied: certifi in /usr/lib/python3.13/site-packages (from httpx>=0.23.0->litellm) (2025.6.15)
Requirement already satisfied: httpcore==1.* in /home/<USER>/.local/lib/python3.13/site-packages (from httpx>=0.23.0->litellm) (1.0.9)
Requirement already satisfied: h11>=0.16 in /home/<USER>/.local/lib/python3.13/site-packages (from httpcore==1.*->httpx>=0.23.0->litellm) (0.16.0)
Requirement already satisfied: zipp>=3.20 in /home/<USER>/.local/lib/python3.13/site-packages (from importlib-metadata>=6.8.0->litellm) (3.23.0)
Requirement already satisfied: distro<2,>=1.7.0 in /usr/lib/python3.13/site-packages (from openai>=1.68.2->litellm) (1.9.0)
Collecting jiter<1,>=0.4.0 (from openai>=1.68.2->litellm)
  Downloading jiter-0.10.0-cp313-cp313-manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (5.2 kB)
Requirement already satisfied: sniffio in /home/<USER>/.local/lib/python3.13/site-packages (from openai>=1.68.2->litellm) (1.3.1)
Requirement already satisfied: tqdm>4 in /usr/lib/python3.13/site-packages (from openai>=1.68.2->litellm) (4.67.1)
Requirement already satisfied: regex>=2022.1.18 in /home/<USER>/.local/lib/python3.13/site-packages (from tiktoken>=0.7.0->litellm) (2024.11.6)
Requirement already satisfied: requests>=2.26.0 in /usr/lib/python3.13/site-packages (from tiktoken>=0.7.0->litellm) (2.32.4)
Requirement already satisfied: charset_normalizer<4,>=2 in /usr/lib/python3.13/site-packages (from requests>=2.26.0->tiktoken>=0.7.0->litellm) (3.4.2)
Requirement already satisfied: urllib3<3,>=1.21.1 in /usr/lib/python3.13/site-packages (from requests>=2.26.0->tiktoken>=0.7.0->litellm) (2.5.0)
Requirement already satisfied: huggingface-hub<1.0,>=0.16.4 in /home/<USER>/.local/lib/python3.13/site-packages (from tokenizers->litellm) (0.33.0)
Requirement already satisfied: filelock in /usr/lib/python3.13/site-packages (from huggingface-hub<1.0,>=0.16.4->tokenizers->litellm) (3.18.0)
Requirement already satisfied: fsspec>=2023.5.0 in /home/<USER>/.local/lib/python3.13/site-packages (from huggingface-hub<1.0,>=0.16.4->tokenizers->litellm) (2025.5.1)
Requirement already satisfied: packaging>=20.9 in /usr/lib/python3.13/site-packages (from huggingface-hub<1.0,>=0.16.4->tokenizers->litellm) (25.0)
Requirement already satisfied: pyyaml>=5.1 in /usr/lib/python3.13/site-packages (from huggingface-hub<1.0,>=0.16.4->tokenizers->litellm) (6.0.2)
Requirement already satisfied: hf-xet<2.0.0,>=1.1.2 in /home/<USER>/.local/lib/python3.13/site-packages (from huggingface-hub<1.0,>=0.16.4->tokenizers->litellm) (1.1.5)
Downloading litellm-1.73.6-py3-none-any.whl (8.5 MB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 8.5/8.5 MB 25.9 MB/s eta 0:00:00
Downloading openai-1.93.0-py3-none-any.whl (755 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 755.0/755.0 kB 31.7 MB/s eta 0:00:00
Downloading jiter-0.10.0-cp313-cp313-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (350 kB)
Installing collected packages: jiter, openai, litellm

Successfully installed jiter-0.10.0 litellm-1.73.6 openai-1.93.0
